from unittest import mock

import pytest
import ujson

from app.documents_ai.db import insert_or_replace_extractions, select_extractions_for_documents
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.documents_ai.types import DocumentDataExtraction, OCRProcessedDataItem
from app.documents_ai.utils import _prepare_output
from app.services import services
from app.tests.common import prepare_client, prepare_document_data
from worker.documents_ai.jobs import process_structured_data_extraction_job
from worker.documents_ai.tests.data import TEST_DOCUMENT_BYTES


def _mock_aws_clients(
    monkeypatch,
    llm_resp_text: str,
    textract_resp=None,
):
    async def mock_get_bytes(*, document_id: str, version_id: str | None = None) -> bytes | None:
        return TEST_DOCUMENT_BYTES

    async def mock_textract_detect(**kwargs):
        if textract_resp:
            return textract_resp
        return {
            'Blocks': [
                {
                    'BlockType': 'LINE',
                    'Text': 'hello',
                    'Geometry': {
                        'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.3, 'Height': 0.1}
                    },
                }
            ]
        }

    class MockAsyncBody:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            return False

        async def read(self) -> bytes:
            return ujson.dumps(
                {
                    'content': [{'text': llm_resp_text}],
                    'usage': {'input_tokens': 1, 'output_tokens': 1},
                }
            ).encode()

    async def mock_bedrock_invoke(**kwargs):
        return {'body': MockAsyncBody()}

    monkeypatch.setattr('app.documents_ai.utils.get_document_bytes_for_extraction', mock_get_bytes)
    monkeypatch.setattr(
        'app.services.services.textract_client.detect_document_text', mock_textract_detect
    )
    monkeypatch.setattr('app.services.services.bedrock_client.invoke_model', mock_bedrock_invoke)


async def test_extraction_job_invalid_structured_data(aiohttp_client, monkeypatch):
    """Test extraction job with invalid structured data that should fail validation."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    _mock_aws_clients(monkeypatch, llm_resp_text='{"invalid": "structured_data"}')

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    import pydantic

    with pytest.raises(pydantic.ValidationError):
        await process_structured_data_extraction_job(
            app,
            data={
                'document_id': document.id,
                'company_id': user.company_id,
            },
            logger=mock.Mock(),
        )

    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.ERROR


async def test_extraction_job_execution(aiohttp_client, monkeypatch):
    """Test successful extraction job execution."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    _mock_aws_clients(
        monkeypatch, llm_resp_text=ujson.dumps({'extracted_data': {'details': {'ok': True}}})
    )

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    await process_structured_data_extraction_job(
        app=app,
        data={
            'document_id': document.id,
            'company_id': user.company_id,
        },
        logger=mock.Mock(),
    )

    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.AWAITING_VALIDATION


async def test_bboxes_from_indexes(aiohttp_client, monkeypatch):
    """Test that bounding boxes are correctly enriched from OCR indexes."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='bbox')

    _mock_aws_clients(
        monkeypatch,
        llm_resp_text=ujson.dumps(
            {
                'extracted_data': {
                    'details': {
                        'number': {'value': 'A-1', 'indexes': [0]},
                        'date_created': {'value': '2024-05-05', 'indexes': [1]},
                    }
                }
            }
        ),
        textract_resp={
            'Blocks': [
                {
                    'BlockType': 'LINE',
                    'Text': 'first',
                    'Page': 1,
                    'Geometry': {
                        'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.3, 'Height': 0.1}
                    },
                },
                {
                    'BlockType': 'LINE',
                    'Text': 'second',
                    'Page': 2,
                    'Geometry': {
                        'BoundingBox': {'Left': 0.0, 'Top': 0.0, 'Width': 0.1, 'Height': 0.2}
                    },
                },
            ]
        },
    )

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    await process_structured_data_extraction_job(
        app,
        data={
            'document_id': document.id,
            'company_id': user.company_id,
        },
        logger=mock.Mock(),
    )

    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.AWAITING_VALIDATION

    ocr_data = [
        OCRProcessedDataItem(text='first', page=0, bbox=[100, 200, 400, 300]),
        OCRProcessedDataItem(text='second', page=1, bbox=[0, 0, 100, 200]),
    ]

    test_data = {
        'details': {
            'number': {'value': 'A-1', 'indexes': [0]},
            'date_created': {'value': '2024-05-05', 'indexes': [1]},
        }
    }

    enriched = _prepare_output(test_data, ocr_data)
    assert 'bboxes' in enriched['details']['number']
    assert enriched['details']['number']['bboxes'][0]['page'] == 0
    assert enriched['details']['date_created']['bboxes'][0]['page'] == 1

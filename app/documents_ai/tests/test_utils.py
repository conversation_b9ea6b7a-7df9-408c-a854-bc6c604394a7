
import pytest

from app.documents_ai.schemas import StructuredDataModel
from app.documents_ai.types import OCRProcessedDataItem
from app.documents_ai.utils import (
    _extract_text_from_document_content_sync,
    _prepare_output,
    _prune_empty_fields,
)

with open('app/documents_ai/tests/files/doc.pdf', 'rb') as f:
    pdf_content = f.read()
with open('app/documents_ai/tests/files/doc.docx', 'rb') as f:
    docx_content = f.read()
with open('app/documents_ai/tests/files/doc.doc', 'rb') as f:
    doc_content = f.read()

CONTENT_MAPPING = {
    '.pdf': pdf_content,
    '.docx': docx_content,
    '.doc': doc_content,
}


@pytest.mark.parametrize(
    'extension',
    ['.pdf', '.docx', '.doc'],
)
def test_extract_text_from_document_content_sync(extension, monkeypatch):
    monkeypatch.setattr('app.documents_ai.utils.MIN_TEXT_LEN', 0)

    content = CONTENT_MAPPING.get(extension)
    result = _extract_text_from_document_content_sync(content=content, extension=extension)
    assert result == 'Some text Of this Document Text with styles 42 wow!!! Ending!'


def test_extract_text_from_document_content_sync_min_text_len(monkeypatch):
    result = _extract_text_from_document_content_sync(content=pdf_content, extension='.pdf')
    assert result is None


def test_extract_text_from_document_content_sync_unsupported_extension():
    content = b'Unsupported content'
    extension = '.txt'
    result = _extract_text_from_document_content_sync(content=content, extension=extension)
    assert result is None


@pytest.mark.parametrize(
    'input_data, expected',
    [
        pytest.param(
            {
                'extracted_data': {
                    'details': {
                        'number': {
                            'value': 'DOC-123',
                            'bboxes': [{'bbox': [1, 2, 3, 4], 'page': 1}],
                        },
                        'date': {'value': None},
                        'empty_field': {},
                    },
                    'items': [
                        {'name': {'value': 'Item 1'}},
                        {'name': {'value': None}},
                        {},
                        {'name': {'value': 'Item 2'}, 'price': {'value': 100}},
                    ],
                    'empty_section': None,
                }
            },
            {
                'extracted_data': {
                    'details': {
                        'number': {
                            'value': 'DOC-123',
                            'bboxes': [{'bbox': [1, 2, 3, 4], 'page': 1}],
                        }
                    },
                    'items': [
                        {'name': {'value': 'Item 1'}},
                        {'name': {'value': 'Item 2'}, 'price': {'value': 100}},
                    ],
                }
            },
            id='nested_fs_objects',
        ),
    ],
)
def test_prune_extracted_data(input_data, expected):
    parsed = StructuredDataModel.model_validate(input_data)
    assert _prune_empty_fields(parsed.to_dict()) == expected


@pytest.mark.parametrize(
    'input_data',
    [
        42,
        3.14,
        True,
        False,
        'string',
        [1, 2, 3],
        {'key': 'value'},
        {'value': 'test', 'bboxes': [{'bbox': [1, 2, 3, 4], 'page': 1}]},
    ],
)
def test_prune_empty_fields_preserves_valid_data(input_data):
    """Test that valid data is preserved unchanged"""
    result = _prune_empty_fields(input_data)
    assert result == input_data


def test_prepare_output_basic():
    """Test _prepare_output with basic data transformation"""
    extracted_data = {
        'extracted_data': {
            'details': {
                'number': {'value': 'DOC-123', 'indexes': ['0', '1']},
                'date_created': {'value': '2024-01-01', 'indexes': ['2']},
            }
        }
    }

    ocr_data = [
        OCRProcessedDataItem(text='DOC', page=1, bbox=[10, 20, 30, 40]),
        OCRProcessedDataItem(text='-123', page=1, bbox=[30, 20, 60, 40]),
        OCRProcessedDataItem(text='2024-01-01', page=1, bbox=[10, 50, 100, 70]),
    ]

    result = _prepare_output(extracted_data, ocr_data)

    # Check that indexes were replaced with bboxes
    number_field = result['extracted_data']['details']['number']
    assert number_field['value'] == 'DOC-123'
    assert len(number_field['bboxes']) == 2
    assert number_field['bboxes'][0] == {'bbox': [10, 20, 30, 40], 'page': 1}
    assert number_field['bboxes'][1] == {'bbox': [30, 20, 60, 40], 'page': 1}

    date_field = result['extracted_data']['details']['date_created']
    assert date_field['value'] == '2024-01-01'
    assert len(date_field['bboxes']) == 1
    assert date_field['bboxes'][0] == {'bbox': [10, 50, 100, 70], 'page': 1}


def test_prepare_output_missing_indexes():
    """Test _prepare_output with missing indexes"""
    extracted_data = {
        'extracted_data': {
            'details': {
                'number': {'value': 'DOC-123', 'indexes': ['0', '999']},  # 999 doesn't exist
            }
        }
    }

    ocr_data = [
        OCRProcessedDataItem(text='DOC', page=1, bbox=[10, 20, 30, 40]),
    ]

    result = _prepare_output(extracted_data, ocr_data)

    # Should only include existing indexes
    number_field = result['extracted_data']['details']['number']
    assert len(number_field['bboxes']) == 1
    assert number_field['bboxes'][0] == {'bbox': [10, 20, 30, 40], 'page': 1}


def test_prepare_output_no_indexes():
    """Test _prepare_output with no indexes field"""
    extracted_data = {
        'extracted_data': {
            'details': {
                'number': {'value': 'DOC-123'},  # No indexes field
            }
        }
    }

    ocr_data = [
        OCRProcessedDataItem(text='DOC', page=1, bbox=[10, 20, 30, 40]),
    ]

    result = _prepare_output(extracted_data, ocr_data)

    # Should not have bboxes field
    number_field = result['extracted_data']['details']['number']
    assert 'bboxes' not in number_field
    assert number_field['value'] == 'DOC-123'


def test_prepare_output_empty_ocr_data():
    """Test _prepare_output with empty OCR data"""
    extracted_data = {
        'extracted_data': {
            'details': {
                'number': {'value': 'DOC-123', 'indexes': ['0']},
            }
        }
    }

    ocr_data = []

    result = _prepare_output(extracted_data, ocr_data)

    # Should not have bboxes since no OCR data available
    number_field = result['extracted_data']['details']['number']
    assert len(number_field.get('bboxes', [])) == 0

from enum import StrEnum, unique


@unique
class ModelProvider(StrEnum):
    anthropic = 'anthropic'
    meta = 'meta'
    amazon = 'amazon'


@unique
class ModelName(StrEnum):
    claude_sonnet_3_5 = 'claude_sonnet_3_5'
    claude_sonnet_3_7 = 'claude_sonnet_3_7'
    claude_haiku_3 = 'claude_haiku_3'

    @property
    def provider(self) -> ModelProvider:
        return ModelProvider.anthropic


@unique
class StructuredDataExtractionStatus(StrEnum):
    PENDING = 'PENDING'
    IN_PROGRESS = 'IN_PROGRESS'
    AWAITING_VALIDATION = 'AWAITING_VALIDATION'
    CONFIRMED = 'CONFIRMED'
    ERROR = 'ERROR'


@unique
class StructuredDataErrorKey(StrEnum):
    INVALID_STRUCTURED_DATA = 'INVALID_STRUCTURED_DATA'
    NO_DOCUMENT_CONTENT = 'NO_DOCUMENT_CONTENT'
    OCR_FAILED = 'OCR_FAILED'
    MODEL_INVOCATION_FAILED = 'MODEL_INVOCATION_FAILED'
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
